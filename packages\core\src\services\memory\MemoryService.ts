import fs from "fs/promises";
import { Context, Service } from "koishi";
import path from "path";
import { v4 as uuidv4 } from "uuid";

import { IChatModel, IEmbedModel, TaskType } from "@/services/model";
import { loadPrompt, loadTemplate, PromptService } from "@/services/prompt";
import { Services, TableName } from "@/services/types";
import { AppError, ErrorCodes } from "@/shared/errors";
import { cosineSimilarity, JsonParser } from "@/shared/utils";
import { MemoryConfig } from "./config";
import { MemoryBlock } from "./MemoryBlock";
import {
    Entity,
    EntityType,
    ExtractedFact,
    ExtractedInsight,
    Fact,
    MemoryBlockData,
    UserProfile,
    MemoryOperationResult,
    SearchOptions,
    EntityMergeOptions,
    ProfileConsolidationOptions
} from "./types";

declare module "koishi" {
    interface Context {
        [Services.Memory]: MemoryService;
    }
    interface Tables {
        [TableName.Entities]: Entity;
        [TableName.Facts]: Fact;
        [TableName.UserProfiles]: UserProfile;
    }
}

/**
 * 记忆服务接口
 * 提供完整的记忆管理功能，包括实体管理、事实存储、搜索和用户画像生成
 */
export interface IMemoryService {
    // === 实体管理 ===
    /**
     * 添加或获取实体
     * @param name 实体名称
     * @param type 实体类型
     * @param metadata 实体元数据
     * @returns 实体对象
     */
    addOrGetEntity(name: string, type: EntityType, metadata?: Record<string, any>): Promise<MemoryOperationResult<Entity>>;

    /**
     * 根据用户ID获取或创建用户实体
     * @param userId 用户ID
     * @param metadata 用户元数据
     * @returns 用户实体
     */
    getOrCreateUserEntity(userId: string, metadata?: Record<string, any>): Promise<MemoryOperationResult<Entity>>;

    /**
     * 查找相似实体
     * @param entity 目标实体
     * @param options 合并选项
     * @returns 相似实体列表
     */
    findSimilarEntities(entity: Entity, options?: EntityMergeOptions): Promise<MemoryOperationResult<Entity[]>>;

    /**
     * 合并重复实体
     * @param sourceEntityId 源实体ID
     * @param targetEntityId 目标实体ID
     * @returns 合并结果
     */
    mergeEntities(sourceEntityId: string, targetEntityId: string): Promise<MemoryOperationResult<Entity>>;

    // === 事实管理 ===
    /**
     * 添加事实
     * @param factData 事实数据
     * @returns 创建的事实
     */
    addFact(factData: Omit<Fact, "id" | "embedding" | "createdAt" | "lastAccessedAt" | "accessCount">): Promise<MemoryOperationResult<Fact>>;

    /**
     * 搜索事实
     * @param query 搜索查询
     * @param options 搜索选项
     * @returns 匹配的事实列表
     */
    searchFacts(query: string, options?: SearchOptions): Promise<MemoryOperationResult<Fact[]>>;

    /**
     * 更新事实访问信息
     * @param factId 事实ID
     * @returns 更新结果
     */
    updateFactAccess(factId: string): Promise<MemoryOperationResult<void>>;

    // === 用户画像管理 ===
    /**
     * 获取用户画像
     * @param entityId 实体ID
     * @returns 用户画像
     */
    getUserProfile(entityId: string): Promise<MemoryOperationResult<UserProfile | null>>;

    /**
     * 整合用户画像
     * @param entityId 实体ID
     * @param options 整合选项
     * @returns 更新后的用户画像
     */
    consolidateProfile(entityId: string, options?: ProfileConsolidationOptions): Promise<MemoryOperationResult<UserProfile | null>>;

    // === 维护操作 ===
    /**
     * 执行记忆衰减与遗忘
     * @returns 操作结果
     */
    decayAndForget(): Promise<MemoryOperationResult<{ removedCount: number }>>;

    /**
     * 清理重复实体
     * @param options 合并选项
     * @returns 清理结果
     */
    deduplicateEntities(options?: EntityMergeOptions): Promise<MemoryOperationResult<{ mergedCount: number }>>;
}

export class MemoryService extends Service<MemoryConfig> implements IMemoryService {
    static readonly inject = [Services.Logger, Services.Prompt, Services.Model, "database"];

    private coreMemoryBlocks: Map<string, MemoryBlock> = new Map();

    private readonly promptService: PromptService;
    private readonly chatModel: IChatModel;
    private readonly embeddingModel: IEmbedModel;
    private readonly jsonParser = new JsonParser<{ facts: ExtractedFact[]; insights: ExtractedInsight[] }>();

    /** 获取日志记录器 */
    private get log() {
        return this.ctx.logger;
    }

    constructor(ctx: Context, config: MemoryConfig) {
        super(ctx, Services.Memory, true);
        this.config = config;
        this.promptService = ctx[Services.Prompt];

        // 从模型服务获取所需的模型实例
        this.chatModel = this.ctx[Services.Model].useChatGroup(TaskType.Memory)?.current;
        this.embeddingModel = this.ctx[Services.Model].useEmbeddingGroup(TaskType.Embedding)?.current;

        if (!this.chatModel || !this.embeddingModel) {
            ctx.logger.warn("聊天模型或嵌入模型不可用，记忆服务功能将受限。");
        }
    }

    protected async start(): Promise<void> {
        this.registerDatabaseModels();
        this.registerPromptTemplates();
        await this.discoverAndLoadCoreMemoryBlocks();

        // 监听消息事件以收集记忆素材
        this.ctx.on("worldstate:summary", (chunkForSummary) => this.handleSummaryChunk(chunkForSummary));

        this.ctx.logger.info("服务已启动，开始监听消息。");
    }

    protected async stop(): Promise<void> {
        this.ctx.logger.info("服务已停止。");
    }

    /**
     * 注册所有数据库模型
     */
    private registerDatabaseModels() {
        this.ctx.model.extend(
            TableName.Entities,
            {
                id: "string(64)",
                type: "string(32)",
                name: "string(255)",
                metadata: "object",
                createdAt: "timestamp",
            },
            { primary: "id" }
        );

        this.ctx.model.extend(
            TableName.Facts,
            {
                id: "string(64)",
                content: "text",
                embedding: "array",
                relatedEntityIds: "array",
                type: "string(32)",
                sourceMessageId: "string(64)",
                salience: "float",
                createdAt: "timestamp",
                lastAccessedAt: "timestamp",
                accessCount: "integer",
            },
            { primary: "id" }
        );

        this.ctx.model.extend(
            TableName.UserProfiles,
            {
                id: "string(64)",
                entityId: "string(64)",
                content: "text",
                embedding: "array",
                confidence: "float",
                supportingFactIds: "array",
                updatedAt: "timestamp",
            },
            { primary: "id", unique: [["entityId"]] }
        );
    }

    private registerPromptTemplates() {
        this.promptService.registerTemplate("memory.fact_extraction", loadPrompt("fact_retrieval"));
        this.promptService.registerTemplate("memory.profile_consolidation", loadTemplate("profile_consolidation"));
    }

    get blocks(): Map<string, MemoryBlock> {
        return this.coreMemoryBlocks;
    }

    public async getMemoryBlocksForRendering(): Promise<MemoryBlockData[]> {
        return Array.from(this.coreMemoryBlocks.values()).map((block) => ({
            title: block.title,
            label: block.label,
            limit: block.limit,
            description: block.description,
            content: block.content as string[],
        }));
    }

    /**
     * 扫描核心记忆目录，加载所有可用的记忆块。
     * @returns
     */
    private async discoverAndLoadCoreMemoryBlocks() {
        const memoryPath = this.config.coreMemoryPath;
        try {
            await fs.mkdir(memoryPath, { recursive: true });
            const files = await fs.readdir(memoryPath);
            const memoryFiles = files.filter((file) => file.endsWith(".md") || file.endsWith(".txt"));

            if (memoryFiles.length === 0) {
                this.ctx.logger.warn(`核心记忆目录 '${memoryPath}' 为空，未加载任何记忆块。`);
                return;
            }

            for (const file of memoryFiles) {
                const filePath = path.join(memoryPath, file);
                try {
                    const block = await MemoryBlock.createFromFile(this.ctx, filePath);
                    if (this.coreMemoryBlocks.has(block.label)) {
                        this.ctx.logger.warn(`发现重复的记忆块标签 '${block.label}'，来自文件 '${filePath}'。已忽略。`);
                    } else {
                        this.coreMemoryBlocks.set(block.label, block);
                        this.ctx.logger.debug(`已从文件 '${file}' 加载核心记忆块 '${block.label}'。`);
                    }
                } catch (error) {
                    //this.ctx.logger.error(`加载记忆块文件 '${filePath}' 失败: ${error.message}`);
                }
            }
        } catch (error) {
            this.ctx.logger.error(`扫描核心记忆目录 '${memoryPath}' 失败: ${error.message}`);
            throw new AppError("Failed to discover core memory blocks", {
                code: ErrorCodes.SERVICE.INITIALIZATION_FAILURE,
                cause: error,
            });
        }
    }

    /**
     * 事件处理主入口：处理从 worldstate 发来的待归档对话片段。
     * @param chunk 包含多用户消息的对话片段
     */
    private async handleSummaryChunk(chunk: string): Promise<void> {
        try {
            // 1. 调用LLM，一次性提取出所有事实和洞察
            const { facts, insights } = await this.extractFromChunk(chunk);
            this.ctx.logger.info(`从 chunk 中提取到 ${facts.length} 条事实和 ${insights.length} 条洞察。`);

            if (facts.length === 0 && insights.length === 0) {
                return;
            }

            // 2. 将事实和洞察合并，并统一处理
            const allMemories = [
                ...facts.map((f) => ({ ...f, memoryType: "fact" })),
                ...insights.map((i) => ({ ...i, memoryType: "insight" })),
            ];

            // 3. 遍历并存储每一条记忆（无论是事实还是洞察）
            for (const memory of allMemories) {
                await this.storeMemory(memory);
            }

            this.ctx.logger.info(`成功处理并存储了 ${allMemories.length} 条新记忆。`);
        } catch (error) {
            this.ctx.logger.error("处理 summary chunk 时出错:", error);
        }
    }

    /**
     * 调用LLM从对话片段中提取事实和洞察。
     * @param chunk 对话片段
     * @returns 提取出的事实和洞察对象
     */
    private async extractFromChunk(chunk: string): Promise<{ facts: ExtractedFact[]; insights: ExtractedInsight[] }> {
        const systemPrompt = await this.promptService.render("memory.fact_extraction");

        const userPrompt = await this.promptService.renderRaw(`Input:\n{{conversationText}}`, {
            conversationText: chunk,
        });

        const { text } = await this.chatModel.chat([
            { role: "system", content: systemPrompt },
            { role: "user", content: userPrompt },
        ]);

        const parsedResponse = this.jsonParser.parse(text);
        if (parsedResponse.error || !parsedResponse.data) {
            this.ctx.logger.error("解析LLM响应失败:", parsedResponse.error);
            return { facts: [], insights: [] };
        }

        let { facts, insights } = parsedResponse.data;

        if (!Array.isArray(facts)) {
            facts = [];
        }
        if (!Array.isArray(insights)) {
            insights = [];
        }

        return { facts, insights };
    }

    /**
     * 存储单条记忆（事实或洞察）到数据库。
     * @param memoryData 从LLM提取并带有元数据的一条记忆
     */
    private async storeMemory(memoryData: ExtractedFact | ExtractedInsight): Promise<void> {
        try {
            // 1. 确保所有相关的实体都已存在于数据库中
            const entityIdSet = new Set<string>();
            if (memoryData.relatedEntities && memoryData.relatedEntities.length > 0) {
                const entityPromises = memoryData.relatedEntities.map((e) =>
                    this.addOrGetEntity(e.name, e.type || EntityType.Unknown, e.metadata)
                );
                const entityResults = await Promise.all(entityPromises);
                entityResults.forEach((result) => {
                    if (result.success && result.data) {
                        entityIdSet.add(result.data.id);
                    }
                });
            }

            // 如果没有任何关联实体，这条记忆是无用的，可以跳过
            if (entityIdSet.size === 0) {
                this.ctx.logger.warn(`跳过一条没有关联任何实体的记忆: "${memoryData.content}"`);
                return;
            }

            // 2. 组装最终要存入数据库的事实数据
            const factToStore: Omit<Fact, "id" | "embedding" | "createdAt" | "lastAccessedAt" | "accessCount"> = {
                content: memoryData.content,
                relatedEntityIds: Array.from(entityIdSet),
                //@ts-ignore
                type: memoryData.type === "insight" ? "behavioral_pattern" : memoryData.type || "statement",
                salience: memoryData.salience || 0.5,
                sourceMessageId: memoryData.sourceMessageId,
            };

            // 3. 调用服务添加事实，这会处理向量化和数据库写入
            const result = await this.addFact(factToStore);
            if (result.success) {
                this.ctx.logger.debug(`成功存储记忆: "${factToStore.content}"`);
            }
        } catch (error) {
            this.ctx.logger.error(`存储单条记忆时出错: "${memoryData.content}"`, error);
        }
    }

    // =================================================================================
    // #region IMemoryService 接口实现
    // =================================================================================

    async addOrGetEntity(name: string, type: EntityType, metadata: Record<string, any> = {}): Promise<MemoryOperationResult<Entity>> {
        try {
            const [existingEntity] = await this.ctx.database.get(TableName.Entities, { name, type });
            if (existingEntity) {
                return { success: true, data: existingEntity };
            }

            const newEntity: Entity = {
                id: `ent_${uuidv4()}`,
                name,
                type,
                metadata,
                createdAt: new Date(),
            };

            const createdEntity = await this.ctx.database.create(TableName.Entities, newEntity);
            return { success: true, data: createdEntity };
        } catch (error) {
            this.ctx.logger.error(`添加或获取实体失败: ${error.message}`, error);
            return { success: false, error: error.message };
        }
    }

    async getOrCreateUserEntity(userId: string, metadata: Record<string, any> = {}): Promise<MemoryOperationResult<Entity>> {
        try {
            // 首先尝试通过 metadata.userId 查找现有实体
            const existingEntities = await this.ctx.database.get(TableName.Entities, {
                type: EntityType.Person
            });

            const existingEntity = existingEntities.find(entity =>
                entity.metadata?.userId === userId
            );

            if (existingEntity) {
                return { success: true, data: existingEntity };
            }

            // 如果没有找到，创建新的用户实体
            const newEntity: Entity = {
                id: `ent_${uuidv4()}`,
                name: metadata.name || metadata.nick || `User_${userId}`,
                type: EntityType.Person,
                metadata: { ...metadata, userId },
                createdAt: new Date(),
            };

            const createdEntity = await this.ctx.database.create(TableName.Entities, newEntity);
            return { success: true, data: createdEntity };
        } catch (error) {
            this.ctx.logger.error(`获取或创建用户实体失败: ${error.message}`, error);
            return { success: false, error: error.message };
        }
    }

    async findSimilarEntities(entity: Entity, options: EntityMergeOptions = {}): Promise<MemoryOperationResult<Entity[]>> {
        try {
            const { similarityThreshold = 0.8 } = options;

            if (!entity.embedding || !this.embeddingModel) {
                return { success: true, data: [] };
            }

            // 获取同类型的所有实体
            const sameTypeEntities = await this.ctx.database.get(TableName.Entities, {
                type: entity.type,
                id: { $ne: entity.id } // 排除自己
            });

            const similarEntities: Entity[] = [];

            for (const otherEntity of sameTypeEntities) {
                if (!otherEntity.embedding) continue;

                const similarity = cosineSimilarity(entity.embedding, otherEntity.embedding);
                if (similarity >= similarityThreshold) {
                    similarEntities.push(otherEntity);
                }
            }

            return { success: true, data: similarEntities };
        } catch (error) {
            this.ctx.logger.error(`查找相似实体失败: ${error.message}`, error);
            return { success: false, error: error.message };
        }
    }

    async mergeEntities(sourceEntityId: string, targetEntityId: string): Promise<MemoryOperationResult<Entity>> {
        try {
            // 获取源实体和目标实体
            const [sourceEntity] = await this.ctx.database.get(TableName.Entities, { id: sourceEntityId });
            const [targetEntity] = await this.ctx.database.get(TableName.Entities, { id: targetEntityId });

            if (!sourceEntity || !targetEntity) {
                return { success: false, error: "源实体或目标实体不存在" };
            }

            // 更新所有引用源实体的事实
            const factsToUpdate = await this.ctx.database.get(TableName.Facts, {
                relatedEntityIds: { $some: [sourceEntityId] }
            });

            for (const fact of factsToUpdate) {
                const updatedEntityIds = fact.relatedEntityIds.map(id =>
                    id === sourceEntityId ? targetEntityId : id
                );
                await this.ctx.database.set(TableName.Facts, { id: fact.id }, {
                    relatedEntityIds: updatedEntityIds
                });
            }

            // 合并元数据
            const mergedMetadata = { ...sourceEntity.metadata, ...targetEntity.metadata };
            await this.ctx.database.set(TableName.Entities, { id: targetEntityId }, {
                metadata: mergedMetadata,
                updatedAt: new Date()
            });

            // 软删除源实体
            await this.ctx.database.set(TableName.Entities, { id: sourceEntityId }, {
                isDeleted: true,
                updatedAt: new Date()
            });

            const [updatedEntity] = await this.ctx.database.get(TableName.Entities, { id: targetEntityId });
            return { success: true, data: updatedEntity };
        } catch (error) {
            this.ctx.logger.error(`合并实体失败: ${error.message}`, error);
            return { success: false, error: error.message };
        }
    }

    async addFact(factData: Omit<Fact, "id" | "embedding" | "createdAt" | "lastAccessedAt" | "accessCount">): Promise<MemoryOperationResult<Fact>> {
        try {
            if (!this.embeddingModel) {
                return { success: false, error: "嵌入模型不可用，无法创建事实。" };
            }

            const embedding = await this.embeddingModel.embed(factData.content).then((res) => res.embedding);

            const newFact: Fact = {
                ...factData,
                id: `fact_${uuidv4()}`,
                embedding,
                createdAt: new Date(),
                lastAccessedAt: new Date(),
                accessCount: 0,
            };

            const createdFact = await this.ctx.database.create(TableName.Facts, newFact);
            return { success: true, data: createdFact };
        } catch (error) {
            this.ctx.logger.error(`添加事实失败: ${error.message}`, error);
            return { success: false, error: error.message };
        }
    }

    async updateFactAccess(factId: string): Promise<MemoryOperationResult<void>> {
        try {
            const [fact] = await this.ctx.database.get(TableName.Facts, { id: factId });
            if (!fact) {
                return { success: false, error: "事实不存在" };
            }

            await this.ctx.database.set(TableName.Facts, { id: factId }, {
                lastAccessedAt: new Date(),
                accessCount: fact.accessCount + 1
            });
            return { success: true };
        } catch (error) {
            this.ctx.logger.error(`更新事实访问信息失败: ${error.message}`, error);
            return { success: false, error: error.message };
        }
    }

    async searchFacts(query: string, options: SearchOptions = {}): Promise<MemoryOperationResult<Fact[]>> {
        try {
            const {
                entityIds = [],
                limit = 10,
                minSalience = 0,
                minSimilarity = 0.3,
                includeDeleted = false
            } = options;

            if (!this.embeddingModel) {
                return { success: false, error: "嵌入模型不可用，无法执行语义搜索。" };
            }

            const queryEmbedding = await this.embeddingModel.embed(query).then((res) => res.embedding);

            // 数据库查询条件
            const dbQuery: any = {
                salience: { $gte: minSalience },
                ...(includeDeleted ? {} : { isDeleted: { $ne: true } })
            };

            if (entityIds.length > 0) {
                dbQuery.relatedEntityIds = { $some: entityIds };
            }

            // **注意：这是一个模拟向量搜索的实现！**
            // 在生产环境中，当事实数量巨大时，此方法效率低下。
            // 强烈建议使用支持原生向量搜索的数据库 (e.g., PostgreSQL + pgvector, Qdrant, Milvus)。
            this.logger.info("正在执行模拟向量搜索。对于大数据集，这可能很慢。");

            const allFacts = await this.ctx.database.get(TableName.Facts, dbQuery);

            if (allFacts.length === 0) {
                return { success: true, data: [] };
            }

            // 在内存中计算相似度
            const factsWithSimilarity = allFacts
                .map((fact) => ({
                    ...fact,
                    similarity: cosineSimilarity(queryEmbedding, fact.embedding)
                }))
                .filter(fact => fact.similarity >= minSimilarity);

            // 按相似度降序排序
            factsWithSimilarity.sort((a, b) => b.similarity - a.similarity);

            // 更新访问信息（异步，不等待结果）
            const topFacts = factsWithSimilarity.slice(0, limit);
            topFacts.forEach(fact => {
                this.updateFactAccess(fact.id).catch(error =>
                    this.logger.warn(`更新事实访问信息失败: ${error}`)
                );
            });

            return { success: true, data: topFacts };
        } catch (error) {
            this.logger.error(`搜索事实失败: ${error.message}`, error);
            return { success: false, error: error.message };
        }
    }

    async getUserProfile(entityId: string): Promise<MemoryOperationResult<UserProfile | null>> {
        try {
            const [profile] = await this.ctx.database.get(TableName.UserProfiles, {
                entityId,
                isDeleted: { $ne: true }
            });
            return { success: true, data: profile || null };
        } catch (error) {
            this.logger.error(`获取用户画像失败: ${error.message}`, error);
            return { success: false, error: error.message };
        }
    }

    public async consolidateProfile(entityId: string, options: ProfileConsolidationOptions = {}): Promise<MemoryOperationResult<UserProfile | null>> {
        try {
            const {
                forceReconsolidate = false,
                minFactsThreshold = 1,
                confidenceThreshold = 0.5
            } = options;

            // 1. 获取实体信息
            const entity = await this.ctx.database.get(TableName.Entities, { id: entityId }).then((res) => res[0]);
            if (!entity || entity.type !== EntityType.Person) {
                return { success: false, error: "实体不存在或不是人员类型" };
            }

            // 2. 获取现有的 Profile
            const existingProfile = await this.ctx.database.get(TableName.UserProfiles, { entityId }).then((res) => res[0]);

            // 3. 获取自上次更新以来，所有新的、未被整合的 Facts
            const newFacts = await this.ctx.database.get(TableName.Facts, {
                relatedEntityIds: { $some: [entityId] },
                isDeleted: { $ne: true }
            });

            if (newFacts.length < minFactsThreshold && !forceReconsolidate) {
                const userId = entity.metadata?.userId || entity.name;
                this.logger.info(`用户 ${userId} 没有足够的新事实需要整合，跳过。`);
                return { success: true, data: existingProfile };
            }

            // 4. 构建 Prompt 输入，使用用户ID而不是用户名
            const userId = entity.metadata?.userId || entity.name;
            const userName = entity.name || `User_${userId}`;

            const inputForLLM = {
                userId: userId,
                userName: userName,
                existingProfile: existingProfile?.content || "This is a new profile for this user.",
                newFactsAndInsights: newFacts.map((f) => `[${f.type}] ${f.content}`),
            };

            // 将 inputForLLM 格式化并填入 PROFILE_CONSOLIDATION_PROMPT 模板
            const prompt = await this.promptService.render("memory.profile_consolidation", inputForLLM);

            // 5. 调用 LLM
            const response = await this.chatModel.chat([{ role: "user", content: prompt }]);

            const parser = new JsonParser<any>();
            const result = parser.parse(response.text);

            if (result.error) {
                this.logger.error(`整合用户画像时出错: ${result.error}`);
                return { success: false, error: `LLM解析失败: ${result.error}` };
            }

            const { profile_content, confidence_score, key_facts_for_update } = result.data;

            // 检查置信度阈值
            if (confidence_score < confidenceThreshold) {
                this.logger.warn(`用户 ${userId} 的画像置信度过低 (${confidence_score})，跳过更新。`);
                return { success: true, data: existingProfile };
            }

            // 6. 更新数据库
            const updatedProfileData = {
                entityId: entityId,
                content: profile_content,
                confidence: confidence_score,
                supportingFactIds: [...(existingProfile?.supportingFactIds || []), ...newFacts.map((f) => f.id)],
                updatedAt: new Date(),
                version: (existingProfile?.version || 0) + 1,
            };

            // 使用 upsert 逻辑：如果profile存在则更新，不存在则创建
            let updatedProfile: UserProfile;
            if (existingProfile) {
                await this.ctx.database.set(TableName.UserProfiles, { id: existingProfile.id }, updatedProfileData);
                updatedProfile = { ...existingProfile, ...updatedProfileData };
            } else {
                updatedProfile = await this.ctx.database.create(TableName.UserProfiles, {
                    id: `profile_${uuidv4()}`,
                    ...updatedProfileData,
                    createdAt: new Date(),
                });
            }

            this.logger.info(`成功为用户 ${userId} 整合并更新了人物画像。`);
            return { success: true, data: updatedProfile };
        } catch (error) {
            this.logger.error(`整合用户画像失败: ${error.message}`, error);
            return { success: false, error: error.message };
        }
    }

    async decayAndForget(): Promise<MemoryOperationResult<{ removedCount: number }>> {
        try {
            this.logger.info("开始执行记忆衰减与遗忘任务...");
            const { stalenessDays, salienceThreshold, accessCountThreshold } = this.config.forgetting;

            const stalenessDate = new Date();
            stalenessDate.setDate(stalenessDate.getDate() - stalenessDays);

            const forgettableFacts = await this.ctx.database.get(TableName.Facts, {
                lastAccessedAt: { $lt: stalenessDate },
                salience: { $lt: salienceThreshold },
                accessCount: { $lt: accessCountThreshold },
                isDeleted: { $ne: true }
            });

            if (forgettableFacts.length > 0) {
                // 使用软删除而不是直接删除
                const idsToRemove = forgettableFacts.map((fact) => fact.id);
                await this.ctx.database.set(TableName.Facts,
                    { id: { $in: idsToRemove } },
                    { isDeleted: true, updatedAt: new Date() }
                );
                this.logger.info(`已遗忘 ${idsToRemove.length} 条陈旧且不重要的事实。`);
                return { success: true, data: { removedCount: idsToRemove.length } };
            } else {
                this.logger.info("没有需要遗忘的事实。");
                return { success: true, data: { removedCount: 0 } };
            }
        } catch (error) {
            this.logger.error(`记忆衰减与遗忘失败: ${error.message}`, error);
            return { success: false, error: error.message };
        }
    }

    async deduplicateEntities(options: EntityMergeOptions = {}): Promise<MemoryOperationResult<{ mergedCount: number }>> {
        try {
            const { similarityThreshold = 0.9, autoMerge = false } = options;
            let mergedCount = 0;

            // 获取所有实体
            const allEntities = await this.ctx.database.get(TableName.Entities, {
                isDeleted: { $ne: true }
            });

            // 按类型分组处理
            const entitiesByType = allEntities.reduce((acc, entity) => {
                if (!acc[entity.type]) acc[entity.type] = [];
                acc[entity.type].push(entity);
                return acc;
            }, {} as Record<string, Entity[]>);

            for (const [type, entities] of Object.entries(entitiesByType)) {
                if (entities.length < 2) continue;

                // 对于每个实体，查找相似的实体
                for (let i = 0; i < entities.length; i++) {
                    const entity = entities[i];
                    if (entity.isDeleted) continue;

                    const similarResult = await this.findSimilarEntities(entity, { similarityThreshold });
                    if (!similarResult.success || !similarResult.data || similarResult.data.length === 0) {
                        continue;
                    }

                    // 如果启用自动合并，则合并相似实体
                    if (autoMerge) {
                        for (const similarEntity of similarResult.data) {
                            if (similarEntity.isDeleted) continue;

                            const mergeResult = await this.mergeEntities(similarEntity.id, entity.id);
                            if (mergeResult.success) {
                                mergedCount++;
                                this.logger.info(`已合并实体: ${similarEntity.name} -> ${entity.name}`);
                            }
                        }
                    }
                }
            }

            return { success: true, data: { mergedCount } };
        } catch (error) {
            this.logger.error(`实体去重失败: ${error.message}`, error);
            return { success: false, error: error.message };
        }
    }
}
